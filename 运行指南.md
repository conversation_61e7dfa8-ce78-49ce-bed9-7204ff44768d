# AI IDE 聊天导出工具 - 快速运行指南

## 🚀 快速启动步骤

### 方法一：使用批处理文件（推荐）

1. 双击运行 `启动服务器.bat` 文件
2. 等待服务器启动完成
3. 在浏览器中访问：`http://127.0.0.1:5000`

### 方法二：手动启动

1. **打开 PowerShell 或命令提示符**
2. **切换到项目目录**：
   ```powershell
   cd "d:\tools\AI-IDE-Chat-Export-Tool"
   ```

3. **启动后端服务器**：
   ```powershell
   cd backend
   python server.py
   ```
   
   或者使用完整路径：
   ```powershell
   & "C:/Program Files/Python313/python.exe" server.py
   ```

4. **打开浏览器访问**：`http://127.0.0.1:5000`

## 📋 系统要求

- ✅ Python 3.7+ （已安装：Python 3.13.5）
- ✅ Flask 和 Flask-CORS （已安装）
- ✅ 前端已构建完成

## 🛑 停止服务器

- 在运行服务器的终端中按 `Ctrl+C`

## 🔧 如果遇到问题

### 依赖缺失
如果提示缺少 Python 包，运行：
```powershell
cd "d:\tools\AI-IDE-Chat-Export-Tool"
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple flask flask-cors
```

### 端口被占用
如果 5000 端口被占用，可以修改 `backend/server.py` 中的端口号。

### Python 路径问题
确保使用正确的 Python 路径：`"C:/Program Files/Python313/python.exe"`

## 📱 应用功能

启动后您可以：
- 查看多种 AI IDE 的聊天记录
- 导出对话为 HTML、JSON、Markdown 格式
- 配置自定义数据源路径
- 智能识别项目信息

---
*最后更新：2025年9月1日*
